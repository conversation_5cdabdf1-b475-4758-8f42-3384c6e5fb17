// Translations for bilingual support
const translations = {
    ar: {
        // Header
        companyName: 'شركة شمرا الزراعية',
        tagline: 'الشريك الموثوق في الزراعة منذ 2015',
        
        // Navigation
        about: 'من نحن',
        products: 'منتجاتنا',
        vision: 'رؤيتنا',
        team: 'فريقنا',
        contact: 'اتصل بنا',
        
        // Hero Section
        heroTitle: 'شركة شمرا الزراعية',
        heroSubtitle: 'شريكك الموثوق في استيراد وتوزيع الأسمدة والبذور الزراعية عالية الجودة',
        heroDescription: 'نخدم المزارعين والمشاريع الزراعية في سوريا بأحدث التقنيات والمنتجات العالمية المتطورة',
        discoverProducts: 'اكتشف منتجاتنا',
        contactUs: 'تواصل معنا',
        
        // About Section
        aboutTitle: 'من نحن',
        aboutSubtitle: 'رائدون في مجال الزراعة المستدامة والحلول الزراعية المتطورة',
        ourStory: 'قصتنا',
        ourExperience: 'خبرتنا',
        yearsExperience: 'سنوات من الخبرة',
        inSyrianMarket: 'في السوق السوري',
        satisfiedCustomers: 'عميل راضٍ',
        farmersAndCompanies: 'من المزارعين والشركات',
        specializedProducts: 'منتج متخصص',
        highQualityFertilizers: 'أسمدة وبذور عالية الجودة',
        ourFounding: 'تأسيسنا',
        foundingYear: 'سنة التأسيس',
        
        // Products Section
        productsTitle: 'ماذا نقدم',
        productsSubtitle: 'نقدم مجموعة شاملة ومتنوعة من المنتجات الزراعية عالية الجودة',
        productsDescription: 'منتجاتنا مختارة بعناية من أفضل المصانع العالمية ومختبرة علمياً لضمان أعلى معايير الجودة والفعالية',
        specializedFertilizers: 'الأسمدة المتخصصة',
        agriculturalSeeds: 'البذور الزراعية',
        agriculturalConsulting: 'الاستشارات الزراعية',
        productGallery: 'معرض المنتجات',
        moreDetails: 'تفاصيل أكثر',
        newProduct: 'جديد',
        
        // Vision Section
        visionMissionTitle: 'رؤيتنا ورسالتنا',
        visionMissionSubtitle: 'نحو مستقبل زراعي مستدام ومزدهر',
        ourVision: 'رؤيتنا',
        ourMission: 'رسالتنا',
        coreValues: 'قيمنا الأساسية',
        quality: 'الجودة',
        trust: 'الثقة',
        innovation: 'الابتكار',
        sustainability: 'الاستدامة',
        
        // Team Section
        teamTitle: 'فريقنا',
        
        // Contact Section
        contactTitle: 'تواصل معنا',
        contactDescription: 'نحن هنا لخدمتكم ومساعدتكم في تحقيق أفضل النتائج الزراعية',
        callUs: 'اتصل بنا',
        emailUs: 'راسلنا',
        
        // Footer
        trustedPartner: 'الشريك الموثوق في الزراعة منذ 2015',
        allRightsReserved: 'جميع الحقوق محفوظة',
        
        // Language
        language: 'اللغة',
        arabic: 'العربية',
        english: 'English'
    },
    
    en: {
        // Header
        companyName: 'Shamra Agricultural Company',
        tagline: 'Your Trusted Partner in Agriculture Since 2015',
        
        // Navigation
        about: 'About Us',
        products: 'Our Products',
        vision: 'Our Vision',
        team: 'Our Team',
        contact: 'Contact Us',
        
        // Hero Section
        heroTitle: 'Shamra Agricultural Company',
        heroSubtitle: 'Your trusted partner in importing and distributing high-quality fertilizers and agricultural seeds',
        heroDescription: 'We serve farmers and agricultural projects in Syria with the latest technologies and advanced global products',
        discoverProducts: 'Discover Our Products',
        contactUs: 'Contact Us',
        
        // About Section
        aboutTitle: 'About Us',
        aboutSubtitle: 'Pioneers in sustainable agriculture and advanced agricultural solutions',
        ourStory: 'Our Story',
        ourExperience: 'Our Experience',
        yearsExperience: 'Years of Experience',
        inSyrianMarket: 'in the Syrian Market',
        satisfiedCustomers: 'Satisfied Customers',
        farmersAndCompanies: 'Farmers and Companies',
        specializedProducts: 'Specialized Products',
        highQualityFertilizers: 'High-Quality Fertilizers and Seeds',
        ourFounding: 'Our Founding',
        foundingYear: 'Year of Establishment',
        
        // Products Section
        productsTitle: 'What We Offer',
        productsSubtitle: 'We offer a comprehensive and diverse range of high-quality agricultural products',
        productsDescription: 'Our products are carefully selected from the best global manufacturers and scientifically tested to ensure the highest standards of quality and effectiveness',
        specializedFertilizers: 'Specialized Fertilizers',
        agriculturalSeeds: 'Agricultural Seeds',
        agriculturalConsulting: 'Agricultural Consulting',
        productGallery: 'Product Gallery',
        moreDetails: 'More Details',
        newProduct: 'New',
        
        // Vision Section
        visionMissionTitle: 'Our Vision & Mission',
        visionMissionSubtitle: 'Towards a sustainable and prosperous agricultural future',
        ourVision: 'Our Vision',
        ourMission: 'Our Mission',
        coreValues: 'Our Core Values',
        quality: 'Quality',
        trust: 'Trust',
        innovation: 'Innovation',
        sustainability: 'Sustainability',
        
        // Team Section
        teamTitle: 'Our Team',
        
        // Contact Section
        contactTitle: 'Contact Us',
        contactDescription: 'We are here to serve you and help you achieve the best agricultural results',
        callUs: 'Call Us',
        emailUs: 'Email Us',
        
        // Footer
        trustedPartner: 'Your Trusted Partner in Agriculture Since 2015',
        allRightsReserved: 'All Rights Reserved',
        
        // Language
        language: 'Language',
        arabic: 'العربية',
        english: 'English'
    }
};

// Language management class
class LanguageManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('language') || 'ar';
        this.init();
    }
    
    init() {
        this.updateLanguage(this.currentLanguage);
        this.setupLanguageSelector();
    }
    
    setupLanguageSelector() {
        // Create language selector if it doesn't exist
        if (!document.getElementById('languageSelector')) {
            const header = document.querySelector('header .container > div');
            if (header) {
                const langSelector = document.createElement('div');
                langSelector.innerHTML = `
                    <select id="languageSelector" class="px-3 py-1 border border-gray-300 rounded text-sm">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                    </select>
                `;
                header.appendChild(langSelector);
            }
        }
        
        const selector = document.getElementById('languageSelector');
        if (selector) {
            selector.value = this.currentLanguage;
            selector.addEventListener('change', (e) => {
                this.changeLanguage(e.target.value);
            });
        }
    }
    
    changeLanguage(lang) {
        this.currentLanguage = lang;
        localStorage.setItem('language', lang);
        this.updateLanguage(lang);
    }
    
    updateLanguage(lang) {
        const html = document.documentElement;
        const body = document.body;
        
        // Update HTML attributes
        html.setAttribute('lang', lang);
        html.setAttribute('dir', lang === 'ar' ? 'rtl' : 'ltr');
        
        // Update body classes for styling
        body.classList.remove('rtl', 'ltr');
        body.classList.add(lang === 'ar' ? 'rtl' : 'ltr');
        
        // Update all translatable elements
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            if (translations[lang] && translations[lang][key]) {
                element.textContent = translations[lang][key];
            }
        });
        
        // Update placeholders
        document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
            const key = element.getAttribute('data-translate-placeholder');
            if (translations[lang] && translations[lang][key]) {
                element.setAttribute('placeholder', translations[lang][key]);
            }
        });
        
        // Update titles
        document.querySelectorAll('[data-translate-title]').forEach(element => {
            const key = element.getAttribute('data-translate-title');
            if (translations[lang] && translations[lang][key]) {
                element.setAttribute('title', translations[lang][key]);
            }
        });
        
        // Update navigation links
        this.updateNavigation(lang);
        
        // Update dynamic content from API
        this.updateDynamicContent(lang);
    }
    
    updateNavigation(lang) {
        const navLinks = document.querySelectorAll('nav a');
        const navTranslations = {
            '#about': lang === 'ar' ? 'من نحن' : 'About Us',
            '#products': lang === 'ar' ? 'منتجاتنا' : 'Our Products',
            '#vision': lang === 'ar' ? 'رؤيتنا' : 'Our Vision',
            '#team': lang === 'ar' ? 'فريقنا' : 'Our Team',
            '#contact': lang === 'ar' ? 'اتصل بنا' : 'Contact Us'
        };
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (navTranslations[href]) {
                link.textContent = navTranslations[href];
            }
        });
    }
    
    async updateDynamicContent(lang) {
        try {
            // Load categories and products with current language
            const categoriesResponse = await fetch('api.php?endpoint=categories&action=list');
            const categoriesData = await categoriesResponse.json();
            
            const productsResponse = await fetch('api.php?endpoint=products&action=list');
            const productsData = await productsResponse.json();
            
            if (categoriesData.success && productsData.success) {
                this.updateProductsSection(categoriesData.data, productsData.data, lang);
            }
        } catch (error) {
            console.error('Error updating dynamic content:', error);
        }
    }
    
    updateProductsSection(categories, products, lang) {
        // Update category cards
        const categoryCards = document.querySelectorAll('.category-card');
        categoryCards.forEach((card, index) => {
            if (categories[index]) {
                const nameElement = card.querySelector('.category-name');
                const descElement = card.querySelector('.category-description');
                
                if (nameElement) {
                    nameElement.textContent = lang === 'ar' ? categories[index].name_ar : categories[index].name_en;
                }
                if (descElement) {
                    descElement.textContent = lang === 'ar' ? categories[index].description_ar : categories[index].description_en;
                }
            }
        });
        
        // Update product cards
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach((card, index) => {
            if (products[index]) {
                const nameElement = card.querySelector('.product-name');
                const descElement = card.querySelector('.product-description');
                
                if (nameElement) {
                    nameElement.textContent = lang === 'ar' ? products[index].name_ar : products[index].name_en;
                }
                if (descElement) {
                    descElement.textContent = lang === 'ar' ? products[index].description_ar : products[index].description_en;
                }
            }
        });
    }
    
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    translate(key) {
        return translations[this.currentLanguage][key] || key;
    }
}

// Initialize language manager
const languageManager = new LanguageManager();
