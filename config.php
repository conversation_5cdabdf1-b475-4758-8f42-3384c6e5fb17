<?php
// Configuration file for Shamra Agriculture Website
session_start();

// Database Configuration (SQLite for shared hosting compatibility)
define('DB_FILE', __DIR__ . '/data/shamra.db');
define('UPLOAD_DIR', __DIR__ . '/uploads/');
define('PRODUCTS_DIR', UPLOAD_DIR . 'products/');
define('CATEGORIES_DIR', UPLOAD_DIR . 'categories/');

// Security Configuration
define('ADMIN_PASSWORD_HASH', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'); // Generated hash for 'Shamrasf@12345'
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'svg', 'webp']);

// Language Configuration
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// Create necessary directories
if (!file_exists(dirname(DB_FILE))) {
    mkdir(dirname(DB_FILE), 0755, true);
}
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}
if (!file_exists(PRODUCTS_DIR)) {
    mkdir(PRODUCTS_DIR, 0755, true);
}
if (!file_exists(CATEGORIES_DIR)) {
    mkdir(CATEGORIES_DIR, 0755, true);
}

// Database Connection
function getDB() {
    try {
        $pdo = new PDO('sqlite:' . DB_FILE);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die('Database connection failed: ' . $e->getMessage());
    }
}

// Initialize Database Tables
function initializeDatabase() {
    $pdo = getDB();
    
    // Categories table
    $pdo->exec("CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name_ar TEXT NOT NULL,
        name_en TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        description_ar TEXT,
        description_en TEXT,
        image TEXT,
        sort_order INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");
    
    // Products table
    $pdo->exec("CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_id INTEGER,
        name_ar TEXT NOT NULL,
        name_en TEXT NOT NULL,
        description_ar TEXT,
        description_en TEXT,
        image TEXT,
        sort_order INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        is_featured INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id)
    )");
    
    // Settings table
    $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )");
    
    // Admin sessions table
    $pdo->exec("CREATE TABLE IF NOT EXISTS admin_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT UNIQUE NOT NULL,
        ip_address TEXT,
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL
    )");
    
    // Insert default categories if they don't exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $defaultCategories = [
            ['الأسمدة المتخصصة', 'Specialized Fertilizers', 'fertilizers', 'أسمدة متخصصة ومركبة ذات فعالية عالية', 'High-efficiency specialized and compound fertilizers'],
            ['البذور الزراعية', 'Agricultural Seeds', 'seeds', 'بذور زراعية عالية الجودة لمحاصيل متنوعة', 'High-quality agricultural seeds for various crops'],
            ['الأسمدة العضوية', 'Organic Fertilizers', 'organic', 'أسمدة عضوية طبيعية صديقة للبيئة', 'Natural organic fertilizers that are environmentally friendly'],
            ['الأسمدة السائلة', 'Liquid Fertilizers', 'liquid', 'أسمدة سائلة سريعة الامتصاص والفعالية', 'Fast-absorbing and effective liquid fertilizers']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO categories (name_ar, name_en, slug, description_ar, description_en, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($defaultCategories as $index => $category) {
            $stmt->execute([$category[0], $category[1], $category[2], $category[3], $category[4], $index + 1]);
        }
    }
}

// Utility Functions
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function generateSlug($text) {
    // Simple slug generation for Arabic and English
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\u0600-\u06FF\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function requireLogin() {
    if (!isLoggedIn()) {
        http_response_code(401);
        echo json_encode(['error' => 'Authentication required']);
        exit;
    }
}

function getCurrentLanguage() {
    return $_SESSION['language'] ?? $_GET['lang'] ?? DEFAULT_LANGUAGE;
}

// Initialize database on first load
if (!file_exists(DB_FILE)) {
    initializeDatabase();
}

// Generate admin password hash (run once)
function generateAdminPasswordHash() {
    $password = 'Shamrasf@12345';
    return password_hash($password, PASSWORD_DEFAULT);
}

// Uncomment the line below to generate the password hash, then comment it back
// echo "Password hash: " . generateAdminPasswordHash() . "\n";
?>
