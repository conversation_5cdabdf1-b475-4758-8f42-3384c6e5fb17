<?php
// Setup script for Shamra Agriculture Website
// Run this once to initialize the database and generate password hash

require_once 'config.php';

echo "<h1>Shamra Agriculture Website Setup</h1>";

// Generate password hash
$password = 'Shamrasf@12345';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "<h2>Password Hash Generated:</h2>";
echo "<p>Copy this hash and update the ADMIN_PASSWORD_HASH constant in config.php:</p>";
echo "<code style='background: #f0f0f0; padding: 10px; display: block; margin: 10px 0;'>";
echo htmlspecialchars($hash);
echo "</code>";

// Initialize database
try {
    initializeDatabase();
    echo "<h2>✅ Database Initialized Successfully</h2>";
    
    // Check if directories exist
    $dirs = [
        'data' => dirname(DB_FILE),
        'uploads' => UPLOAD_DIR,
        'products' => PRODUCTS_DIR,
        'categories' => CATEGORIES_DIR
    ];
    
    echo "<h2>Directory Status:</h2>";
    foreach ($dirs as $name => $path) {
        if (is_dir($path)) {
            echo "<p>✅ {$name}: {$path}</p>";
        } else {
            echo "<p>❌ {$name}: {$path} (not found)</p>";
        }
    }
    
    // Test database connection
    $pdo = getDB();
    
    // Count existing data
    $stmt = $pdo->query("SELECT COUNT(*) FROM categories");
    $categoryCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $productCount = $stmt->fetchColumn();
    
    echo "<h2>Database Content:</h2>";
    echo "<p>Categories: {$categoryCount}</p>";
    echo "<p>Products: {$productCount}</p>";
    
    // Test file permissions
    echo "<h2>File Permissions:</h2>";
    $testFile = UPLOAD_DIR . 'test.txt';
    if (file_put_contents($testFile, 'test')) {
        echo "<p>✅ Upload directory is writable</p>";
        unlink($testFile);
    } else {
        echo "<p>❌ Upload directory is not writable</p>";
    }
    
    echo "<h2>Next Steps:</h2>";
    echo "<ol>";
    echo "<li>Update the ADMIN_PASSWORD_HASH in config.php with the hash above</li>";
    echo "<li>Delete this setup.php file for security</li>";
    echo "<li>Access admin.html to manage your website</li>";
    echo "<li>Visit index.html to see your website</li>";
    echo "</ol>";
    
    echo "<h2>Admin Login:</h2>";
    echo "<p>URL: <a href='admin.html'>admin.html</a></p>";
    echo "<p>Password: Shamrasf@12345</p>";
    
    echo "<h2>Website:</h2>";
    echo "<p>URL: <a href='index.html'>index.html</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Setup Error:</h2>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
h1, h2 { color: #2d5016; }
code { background: #f0f0f0; padding: 2px 4px; }
p { margin: 5px 0; }
ol { margin: 10px 0; }
</style>";
?>
