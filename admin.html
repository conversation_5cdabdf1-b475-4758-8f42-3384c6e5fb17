<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - شركة شمرا الزراعية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    },
                    colors: {
                        'shamra-green': '#2d5016',
                        'shamra-light-green': '#4a7c59',
                        'shamra-gold': '#d4af37',
                    }
                }
            }
        }
    </script>
    <style>
        body { font-family: 'IBM Plex Sans Arabic', sans-serif; }
        .modal { display: none; }
        .modal.active { display: flex; }
        .sidebar-item:hover { background-color: rgba(74, 124, 89, 0.1); }
        .sidebar-item.active { background-color: rgba(74, 124, 89, 0.2); border-right: 4px solid #4a7c59; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Login Modal -->
    <div id="loginModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-shamra-green mb-2">تسجيل الدخول</h2>
                <p class="text-gray-600">لوحة تحكم شركة شمرا الزراعية</p>
            </div>
            <form id="loginForm">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">كلمة المرور</label>
                    <input type="password" id="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-shamra-green" required>
                </div>
                <button type="submit" class="w-full bg-shamra-green text-white py-2 px-4 rounded-lg hover:bg-shamra-light-green transition-colors">
                    دخول
                </button>
                <div id="loginError" class="mt-4 text-red-600 text-sm hidden"></div>
            </form>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="hidden">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-shamra-green">لوحة التحكم - شركة شمرا الزراعية</h1>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <select id="languageSelect" class="px-3 py-1 border border-gray-300 rounded">
                        <option value="ar">العربية</option>
                        <option value="en">English</option>
                    </select>
                    <button id="logoutBtn" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors">
                        خروج
                    </button>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- Sidebar -->
            <aside class="w-64 bg-white shadow-sm h-screen">
                <nav class="mt-4">
                    <a href="#" class="sidebar-item active flex items-center px-6 py-3 text-gray-700" data-section="dashboard">
                        <svg class="w-5 h-5 ml-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                        الرئيسية
                    </a>
                    <a href="#" class="sidebar-item flex items-center px-6 py-3 text-gray-700" data-section="categories">
                        <svg class="w-5 h-5 ml-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
                        </svg>
                        الفئات
                    </a>
                    <a href="#" class="sidebar-item flex items-center px-6 py-3 text-gray-700" data-section="products">
                        <svg class="w-5 h-5 ml-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                        المنتجات
                    </a>
                    <a href="#" class="sidebar-item flex items-center px-6 py-3 text-gray-700" data-section="files">
                        <svg class="w-5 h-5 ml-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                        </svg>
                        إدارة الملفات
                    </a>
                    <a href="#" class="sidebar-item flex items-center px-6 py-3 text-gray-700" data-section="settings">
                        <svg class="w-5 h-5 ml-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                        </svg>
                        الإعدادات
                    </a>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-6">
                <!-- Dashboard Section -->
                <div id="dashboardSection" class="content-section">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">مرحباً بك في لوحة التحكم</h2>
                        <p class="text-gray-600">إدارة محتوى موقع شركة شمرا الزراعية</p>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-shamra-green rounded-lg">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"/>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">إجمالي الفئات</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalCategories">-</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-shamra-light-green rounded-lg">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">إجمالي المنتجات</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalProducts">-</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-shamra-gold rounded-lg">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">الصور المرفوعة</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalImages">-</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-600 rounded-lg">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">المنتجات المميزة</p>
                                    <p class="text-2xl font-bold text-gray-900" id="featuredProducts">-</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">الإجراءات السريعة</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button class="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-shamra-green transition-colors" onclick="showSection('categories')">
                                <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                </svg>
                                <p class="text-sm font-medium text-gray-600">إدارة الفئات</p>
                            </button>
                            <button class="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-shamra-green transition-colors" onclick="showSection('products')">
                                <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                                </svg>
                                <p class="text-sm font-medium text-gray-600">إدارة المنتجات</p>
                            </button>
                            <button class="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-shamra-green transition-colors" onclick="showSection('files')">
                                <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                                </svg>
                                <p class="text-sm font-medium text-gray-600">رفع الصور</p>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Categories Section -->
                <div id="categoriesSection" class="content-section hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">إدارة الفئات</h2>
                        <button id="addCategoryBtn" class="bg-shamra-green text-white px-4 py-2 rounded-lg hover:bg-shamra-light-green transition-colors">
                            إضافة فئة جديدة
                        </button>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوصف</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الترتيب</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="categoriesTable" class="bg-white divide-y divide-gray-200">
                                <!-- Categories will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Products Section -->
                <div id="productsSection" class="content-section hidden">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">إدارة المنتجات</h2>
                        <button id="addProductBtn" class="bg-shamra-green text-white px-4 py-2 rounded-lg hover:bg-shamra-light-green transition-colors">
                            إضافة منتج جديد
                        </button>
                    </div>
                    
                    <div class="mb-4">
                        <select id="categoryFilter" class="px-3 py-2 border border-gray-300 rounded-lg">
                            <option value="">جميع الفئات</option>
                        </select>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصورة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مميز</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="productsTable" class="bg-white divide-y divide-gray-200">
                                <!-- Products will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Files Section -->
                <div id="filesSection" class="content-section hidden">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">إدارة الملفات</h2>
                        <p class="text-gray-600">رفع وإدارة صور المنتجات والفئات</p>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Upload Area -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">رفع ملف جديد</h3>
                            <div id="uploadArea" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-shamra-green transition-colors cursor-pointer">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <p class="text-gray-600 mb-2">اسحب الملفات هنا أو انقر للاختيار</p>
                                <p class="text-sm text-gray-500">JPG, PNG, SVG, WEBP (حد أقصى 10MB)</p>
                                <input type="file" id="fileInput" class="hidden" accept=".jpg,.jpeg,.png,.svg,.webp" multiple>
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">نوع الملف</label>
                                <select id="uploadType" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                    <option value="product">صورة منتج</option>
                                    <option value="category">صورة فئة</option>
                                </select>
                            </div>
                            
                            <div id="uploadProgress" class="mt-4 hidden">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div id="progressBar" class="bg-shamra-green h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <p id="uploadStatus" class="text-sm text-gray-600 mt-2">جاري الرفع...</p>
                            </div>
                        </div>
                        
                        <!-- Recent Files -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4">الملفات الحديثة</h3>
                            <div id="recentFiles" class="space-y-3">
                                <!-- Recent files will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Section -->
                <div id="settingsSection" class="content-section hidden">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">الإعدادات</h2>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <form id="settingsForm">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة (عربي)</label>
                                    <input type="text" id="companyNameAr" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="شركة شمرا الزراعية">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة (إنجليزي)</label>
                                    <input type="text" id="companyNameEn" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="Shamra Agricultural Company">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الشعار (عربي)</label>
                                    <input type="text" id="taglineAr" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="الشريك الموثوق في الزراعة">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الشعار (إنجليزي)</label>
                                    <input type="text" id="taglineEn" class="w-full px-3 py-2 border border-gray-300 rounded-lg" value="Your Trusted Partner in Agriculture">
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <button type="submit" class="bg-shamra-green text-white px-6 py-2 rounded-lg hover:bg-shamra-light-green transition-colors">
                                    حفظ الإعدادات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals will be added here -->
    <div id="modals"></div>

    <script src="admin.js"></script>
</body>
</html>
