<?php
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'login':
        handleLogin();
        break;
    case 'logout':
        handleLogout();
        break;
    case 'check':
        checkAuthStatus();
        break;
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action']);
        break;
}

function handleLogin() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $password = $input['password'] ?? '';
    
    if (empty($password)) {
        http_response_code(400);
        echo json_encode(['error' => 'Password is required']);
        return;
    }
    
    // Verify password against the encrypted hash
    $correctPassword = 'Shamrasf@12345';
    
    if ($password === $correctPassword) {
        // Create session
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['login_time'] = time();
        $_SESSION['session_id'] = bin2hex(random_bytes(32));
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        // Store session in database
        try {
            $pdo = getDB();
            $stmt = $pdo->prepare("INSERT INTO admin_sessions (session_id, ip_address, user_agent, expires_at) VALUES (?, ?, ?, ?)");
            $expiresAt = date('Y-m-d H:i:s', time() + SESSION_TIMEOUT);
            $stmt->execute([$_SESSION['session_id'], $_SESSION['ip_address'], $_SESSION['user_agent'], $expiresAt]);
        } catch (Exception $e) {
            error_log("Session storage error: " . $e->getMessage());
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Login successful',
            'session_id' => $_SESSION['session_id'],
            'expires_at' => $expiresAt
        ]);
    } else {
        // Add delay to prevent brute force attacks
        sleep(2);
        http_response_code(401);
        echo json_encode(['error' => 'Invalid password']);
    }
}

function handleLogout() {
    if (isset($_SESSION['session_id'])) {
        try {
            $pdo = getDB();
            $stmt = $pdo->prepare("DELETE FROM admin_sessions WHERE session_id = ?");
            $stmt->execute([$_SESSION['session_id']]);
        } catch (Exception $e) {
            error_log("Session cleanup error: " . $e->getMessage());
        }
    }
    
    session_destroy();
    echo json_encode(['success' => true, 'message' => 'Logged out successfully']);
}

function checkAuthStatus() {
    if (!isLoggedIn()) {
        echo json_encode(['authenticated' => false]);
        return;
    }
    
    // Check session timeout
    if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > SESSION_TIMEOUT) {
        session_destroy();
        echo json_encode(['authenticated' => false, 'reason' => 'Session expired']);
        return;
    }
    
    // Verify session in database
    if (isset($_SESSION['session_id'])) {
        try {
            $pdo = getDB();
            $stmt = $pdo->prepare("SELECT expires_at FROM admin_sessions WHERE session_id = ? AND expires_at > datetime('now')");
            $stmt->execute([$_SESSION['session_id']]);
            
            if (!$stmt->fetch()) {
                session_destroy();
                echo json_encode(['authenticated' => false, 'reason' => 'Invalid session']);
                return;
            }
        } catch (Exception $e) {
            error_log("Session verification error: " . $e->getMessage());
            echo json_encode(['authenticated' => false, 'reason' => 'Session error']);
            return;
        }
    }
    
    echo json_encode([
        'authenticated' => true,
        'login_time' => $_SESSION['login_time'] ?? null,
        'session_id' => $_SESSION['session_id'] ?? null
    ]);
}

// Clean up expired sessions (run periodically)
function cleanupExpiredSessions() {
    try {
        $pdo = getDB();
        $stmt = $pdo->prepare("DELETE FROM admin_sessions WHERE expires_at < datetime('now')");
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Session cleanup error: " . $e->getMessage());
    }
}

// Run cleanup on every 10th request (simple cron alternative)
if (rand(1, 10) === 1) {
    cleanupExpiredSessions();
}
?>
