# Shamra Agricultural Company Website

A professional bilingual (Arabic/English) website with admin backend for managing agricultural products and content.

## Features

### 🌐 **Bilingual Support**
- Arabic (RTL) and English (LTR) language switching
- Professional Arabic typography with IBM Plex Sans Arabic font
- Complete translation system for all content

### 🔐 **Secure Admin Panel**
- Password-protected admin dashboard
- Encrypted password: `<PERSON>ham<PERSON>f@12345`
- Session management with timeout
- Secure file upload system

### 📁 **Product Management**
- Dynamic product categories
- Image upload with multiple format support (JPG, JPEG, PNG, SVG, WEBP)
- Product descriptions in both languages
- Featured products system
- Drag & drop file upload

### 🎨 **Professional Design**
- Modern, responsive design
- Agricultural color scheme (greens, gold, earth tones)
- Smooth animations and hover effects
- Mobile-friendly layout

### 🗄️ **Database System**
- SQLite database (perfect for shared hosting)
- Categories and products management
- Settings storage
- Admin session tracking

## Installation

### 1. **Upload Files**
Upload all files to your web server directory.

### 2. **Run Setup**
Visit `setup.php` in your browser to:
- Initialize the database
- Generate password hash
- Check file permissions
- Create necessary directories

### 3. **Configure**
Copy the generated password hash from setup.php and update `config.php`:
```php
define('ADMIN_PASSWORD_HASH', 'your_generated_hash_here');
```

### 4. **Security**
Delete `setup.php` after successful installation.

## File Structure

```
shamra/
├── index.html              # Main bilingual website
├── admin.html              # Admin dashboard
├── admin.js                # Admin functionality
├── config.php              # Configuration & database setup
├── auth.php                # Authentication system
├── api.php                 # API endpoints
├── translations.js         # Language translations
├── setup.php               # Initial setup (delete after use)
├── data/                   # Database directory
│   └── shamra.db          # SQLite database
├── uploads/                # File uploads
│   ├── products/          # Product images
│   └── categories/        # Category images
└── products/              # Static product images (fallback)
    ├── fertilizer-1.svg
    ├── seeds-1.svg
    ├── organic-fertilizer.svg
    └── liquid-fertilizer.svg
```

## Usage

### **Admin Access**
1. Visit `admin.html`
2. Enter password: `Shamrasf@12345`
3. Manage categories, products, and files

### **Admin Features**
- **Dashboard**: Overview statistics and quick actions
- **Categories**: Create and manage product categories
- **Products**: Add/edit products with images and descriptions
- **File Manager**: Upload and organize images
- **Settings**: Configure website content

### **Language Switching**
- Use the language selector in the header
- All content automatically switches between Arabic and English
- Language preference is saved in browser

### **Product Management**
1. **Add Category**: Create product categories with Arabic/English names
2. **Add Product**: Assign products to categories with bilingual content
3. **Upload Images**: Drag & drop or click to upload product images
4. **Featured Products**: Mark products as featured for homepage display

## API Endpoints

### Authentication
- `auth.php?action=login` - Admin login
- `auth.php?action=logout` - Admin logout
- `auth.php?action=check` - Check authentication status

### Categories
- `api.php?endpoint=categories&action=list` - Get all categories
- `api.php?endpoint=categories` (POST) - Create category
- `api.php?endpoint=categories&id=X` (PUT) - Update category
- `api.php?endpoint=categories&id=X` (DELETE) - Delete category

### Products
- `api.php?endpoint=products&action=list` - Get all products
- `api.php?endpoint=products` (POST) - Create product
- `api.php?endpoint=products&id=X` (PUT) - Update product
- `api.php?endpoint=products&id=X` (DELETE) - Delete product

### File Upload
- `api.php?endpoint=upload` (POST) - Upload files

### Settings
- `api.php?endpoint=settings` - Get/update settings

## Security Features

- **Password Encryption**: Uses PHP's password_hash() with strong algorithms
- **Session Management**: Secure session handling with timeout
- **File Upload Validation**: Strict file type and size validation
- **SQL Injection Protection**: PDO prepared statements
- **XSS Protection**: Input sanitization and output escaping

## Customization

### **Colors**
Update the color scheme in both `index.html` and `admin.html`:
```javascript
colors: {
    'shamra-green': '#2d5016',
    'shamra-light-green': '#4a7c59',
    'shamra-gold': '#d4af37',
}
```

### **Translations**
Add or modify translations in `translations.js`:
```javascript
const translations = {
    ar: { key: 'Arabic text' },
    en: { key: 'English text' }
};
```

### **Database**
The SQLite database is automatically created with these tables:
- `categories` - Product categories
- `products` - Products with category relationships
- `settings` - Website settings
- `admin_sessions` - Admin session tracking

## Troubleshooting

### **Common Issues**

1. **Database Error**: Ensure the `data/` directory is writable
2. **Upload Failed**: Check `uploads/` directory permissions
3. **Login Issues**: Verify password hash in `config.php`
4. **Images Not Loading**: Check file paths and permissions

### **File Permissions**
Set these permissions on your server:
- `data/` directory: 755 or 777
- `uploads/` directory: 755 or 777
- PHP files: 644

### **Shared Hosting**
This system is designed for shared hosting and uses:
- SQLite (no MySQL required)
- Standard PHP features
- File-based sessions
- No special server requirements

## Support

For technical support or customization requests, contact the development team.

## License

© 2025 Shamra Agricultural Company. All rights reserved.
