{"name": "shamra-agriculture-website", "version": "1.0.0", "description": "Bilingual agriculture website with admin backend for Shamra Agricultural Company", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["agriculture", "bilingual", "admin", "nodejs", "express"], "author": "Shamra Agricultural Company", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "express-fileupload": "^1.4.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "fs-extra": "^11.1.1", "path": "^0.12.7", "uuid": "^9.0.0", "sharp": "^0.32.1", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}