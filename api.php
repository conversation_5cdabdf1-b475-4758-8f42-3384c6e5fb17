<?php
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$endpoint = $_GET['endpoint'] ?? '';
$action = $_GET['action'] ?? '';

// Route requests
switch ($endpoint) {
    case 'categories':
        handleCategories($method, $action);
        break;
    case 'products':
        handleProducts($method, $action);
        break;
    case 'upload':
        handleFileUpload();
        break;
    case 'settings':
        handleSettings($method, $action);
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
        break;
}

// Categories Management
function handleCategories($method, $action) {
    $pdo = getDB();
    
    switch ($method) {
        case 'GET':
            if ($action === 'list') {
                $stmt = $pdo->query("SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order, name_ar");
                $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $categories]);
            } else {
                $id = $_GET['id'] ?? null;
                if ($id) {
                    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
                    $stmt->execute([$id]);
                    $category = $stmt->fetch(PDO::FETCH_ASSOC);
                    echo json_encode(['success' => true, 'data' => $category]);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Category ID required']);
                }
            }
            break;
            
        case 'POST':
            requireLogin();
            $input = json_decode(file_get_contents('php://input'), true);
            
            $nameAr = sanitizeInput($input['name_ar'] ?? '');
            $nameEn = sanitizeInput($input['name_en'] ?? '');
            $descAr = sanitizeInput($input['description_ar'] ?? '');
            $descEn = sanitizeInput($input['description_en'] ?? '');
            $slug = generateSlug($input['slug'] ?? $nameEn);
            $sortOrder = intval($input['sort_order'] ?? 0);
            
            if (empty($nameAr) || empty($nameEn)) {
                http_response_code(400);
                echo json_encode(['error' => 'Name in both languages is required']);
                return;
            }
            
            try {
                $stmt = $pdo->prepare("INSERT INTO categories (name_ar, name_en, slug, description_ar, description_en, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$nameAr, $nameEn, $slug, $descAr, $descEn, $sortOrder]);
                
                $categoryId = $pdo->lastInsertId();
                echo json_encode(['success' => true, 'message' => 'Category created successfully', 'id' => $categoryId]);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
            }
            break;
            
        case 'PUT':
            requireLogin();
            $id = $_GET['id'] ?? null;
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Category ID required']);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $nameAr = sanitizeInput($input['name_ar'] ?? '');
            $nameEn = sanitizeInput($input['name_en'] ?? '');
            $descAr = sanitizeInput($input['description_ar'] ?? '');
            $descEn = sanitizeInput($input['description_en'] ?? '');
            $slug = generateSlug($input['slug'] ?? $nameEn);
            $sortOrder = intval($input['sort_order'] ?? 0);
            $isActive = intval($input['is_active'] ?? 1);
            
            try {
                $stmt = $pdo->prepare("UPDATE categories SET name_ar = ?, name_en = ?, slug = ?, description_ar = ?, description_en = ?, sort_order = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$nameAr, $nameEn, $slug, $descAr, $descEn, $sortOrder, $isActive, $id]);
                
                echo json_encode(['success' => true, 'message' => 'Category updated successfully']);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
            }
            break;
            
        case 'DELETE':
            requireLogin();
            $id = $_GET['id'] ?? null;
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Category ID required']);
                return;
            }
            
            try {
                // Soft delete - just mark as inactive
                $stmt = $pdo->prepare("UPDATE categories SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$id]);
                
                echo json_encode(['success' => true, 'message' => 'Category deleted successfully']);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
            }
            break;
    }
}

// Products Management
function handleProducts($method, $action) {
    $pdo = getDB();
    
    switch ($method) {
        case 'GET':
            if ($action === 'list') {
                $categoryId = $_GET['category_id'] ?? null;
                $sql = "SELECT p.*, c.name_ar as category_name_ar, c.name_en as category_name_en 
                        FROM products p 
                        LEFT JOIN categories c ON p.category_id = c.id 
                        WHERE p.is_active = 1";
                
                if ($categoryId) {
                    $sql .= " AND p.category_id = " . intval($categoryId);
                }
                
                $sql .= " ORDER BY p.sort_order, p.name_ar";
                
                $stmt = $pdo->query($sql);
                $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
                echo json_encode(['success' => true, 'data' => $products]);
            } else {
                $id = $_GET['id'] ?? null;
                if ($id) {
                    $stmt = $pdo->prepare("SELECT p.*, c.name_ar as category_name_ar, c.name_en as category_name_en 
                                          FROM products p 
                                          LEFT JOIN categories c ON p.category_id = c.id 
                                          WHERE p.id = ?");
                    $stmt->execute([$id]);
                    $product = $stmt->fetch(PDO::FETCH_ASSOC);
                    echo json_encode(['success' => true, 'data' => $product]);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Product ID required']);
                }
            }
            break;
            
        case 'POST':
            requireLogin();
            $input = json_decode(file_get_contents('php://input'), true);
            
            $categoryId = intval($input['category_id'] ?? 0);
            $nameAr = sanitizeInput($input['name_ar'] ?? '');
            $nameEn = sanitizeInput($input['name_en'] ?? '');
            $descAr = sanitizeInput($input['description_ar'] ?? '');
            $descEn = sanitizeInput($input['description_en'] ?? '');
            $image = sanitizeInput($input['image'] ?? '');
            $sortOrder = intval($input['sort_order'] ?? 0);
            $isFeatured = intval($input['is_featured'] ?? 0);
            
            if (empty($nameAr) || empty($nameEn) || $categoryId <= 0) {
                http_response_code(400);
                echo json_encode(['error' => 'Name in both languages and valid category are required']);
                return;
            }
            
            try {
                $stmt = $pdo->prepare("INSERT INTO products (category_id, name_ar, name_en, description_ar, description_en, image, sort_order, is_featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$categoryId, $nameAr, $nameEn, $descAr, $descEn, $image, $sortOrder, $isFeatured]);
                
                $productId = $pdo->lastInsertId();
                echo json_encode(['success' => true, 'message' => 'Product created successfully', 'id' => $productId]);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
            }
            break;
            
        case 'PUT':
            requireLogin();
            $id = $_GET['id'] ?? null;
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Product ID required']);
                return;
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            
            $categoryId = intval($input['category_id'] ?? 0);
            $nameAr = sanitizeInput($input['name_ar'] ?? '');
            $nameEn = sanitizeInput($input['name_en'] ?? '');
            $descAr = sanitizeInput($input['description_ar'] ?? '');
            $descEn = sanitizeInput($input['description_en'] ?? '');
            $image = sanitizeInput($input['image'] ?? '');
            $sortOrder = intval($input['sort_order'] ?? 0);
            $isFeatured = intval($input['is_featured'] ?? 0);
            $isActive = intval($input['is_active'] ?? 1);
            
            try {
                $stmt = $pdo->prepare("UPDATE products SET category_id = ?, name_ar = ?, name_en = ?, description_ar = ?, description_en = ?, image = ?, sort_order = ?, is_featured = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$categoryId, $nameAr, $nameEn, $descAr, $descEn, $image, $sortOrder, $isFeatured, $isActive, $id]);
                
                echo json_encode(['success' => true, 'message' => 'Product updated successfully']);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
            }
            break;
            
        case 'DELETE':
            requireLogin();
            $id = $_GET['id'] ?? null;
            if (!$id) {
                http_response_code(400);
                echo json_encode(['error' => 'Product ID required']);
                return;
            }
            
            try {
                // Get product image to delete file
                $stmt = $pdo->prepare("SELECT image FROM products WHERE id = ?");
                $stmt->execute([$id]);
                $product = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Soft delete - mark as inactive
                $stmt = $pdo->prepare("UPDATE products SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $stmt->execute([$id]);
                
                // Optionally delete the image file
                if ($product && $product['image']) {
                    $imagePath = PRODUCTS_DIR . $product['image'];
                    if (file_exists($imagePath)) {
                        unlink($imagePath);
                    }
                }
                
                echo json_encode(['success' => true, 'message' => 'Product deleted successfully']);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
            }
            break;
    }
}

// File Upload Handler
function handleFileUpload() {
    requireLogin();
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        return;
    }
    
    if (!isset($_FILES['file'])) {
        http_response_code(400);
        echo json_encode(['error' => 'No file uploaded']);
        return;
    }
    
    $file = $_FILES['file'];
    $uploadType = $_POST['type'] ?? 'product'; // 'product' or 'category'
    
    // Validate file
    if ($file['error'] !== UPLOAD_ERR_OK) {
        http_response_code(400);
        echo json_encode(['error' => 'File upload error']);
        return;
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        http_response_code(400);
        echo json_encode(['error' => 'File too large']);
        return;
    }
    
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($extension, ALLOWED_EXTENSIONS)) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid file type']);
        return;
    }
    
    // Generate unique filename
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $uploadDir = ($uploadType === 'category') ? CATEGORIES_DIR : PRODUCTS_DIR;
    $uploadPath = $uploadDir . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
        echo json_encode([
            'success' => true,
            'message' => 'File uploaded successfully',
            'filename' => $filename,
            'path' => $uploadPath,
            'url' => str_replace(__DIR__, '', $uploadPath)
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to save file']);
    }
}

// Settings Management
function handleSettings($method, $action) {
    $pdo = getDB();
    
    switch ($method) {
        case 'GET':
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
            $settings = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
            echo json_encode(['success' => true, 'data' => $settings]);
            break;
            
        case 'POST':
            requireLogin();
            $input = json_decode(file_get_contents('php://input'), true);
            
            foreach ($input as $key => $value) {
                $stmt = $pdo->prepare("INSERT OR REPLACE INTO settings (setting_key, setting_value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)");
                $stmt->execute([sanitizeInput($key), sanitizeInput($value)]);
            }
            
            echo json_encode(['success' => true, 'message' => 'Settings updated successfully']);
            break;
    }
}
?>
