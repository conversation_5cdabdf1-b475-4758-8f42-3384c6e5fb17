// Admin Dashboard JavaScript
class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.isAuthenticated = false;
        this.categories = [];
        this.products = [];
        
        this.init();
    }
    
    async init() {
        await this.checkAuth();
        this.setupEventListeners();
        
        if (this.isAuthenticated) {
            this.showDashboard();
            await this.loadDashboardData();
        } else {
            this.showLogin();
        }
    }
    
    async checkAuth() {
        try {
            const response = await fetch('auth.php?action=check');
            const data = await response.json();
            this.isAuthenticated = data.authenticated;
        } catch (error) {
            console.error('Auth check failed:', error);
            this.isAuthenticated = false;
        }
    }
    
    setupEventListeners() {
        // Login form
        document.getElementById('loginForm').addEventListener('submit', (e) => this.handleLogin(e));
        
        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => this.handleLogout());
        
        // Sidebar navigation
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.dataset.section;
                this.showSection(section);
            });
        });
        
        // Language selector
        document.getElementById('languageSelect').addEventListener('change', (e) => {
            this.changeLanguage(e.target.value);
        });
        
        // File upload
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Add buttons
        document.getElementById('addCategoryBtn').addEventListener('click', () => this.showCategoryModal());
        document.getElementById('addProductBtn').addEventListener('click', () => this.showProductModal());
        
        // Category filter
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.filterProducts(e.target.value);
        });
        
        // Settings form
        document.getElementById('settingsForm').addEventListener('submit', (e) => this.saveSettings(e));
    }
    
    async handleLogin(e) {
        e.preventDefault();
        
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('loginError');
        
        try {
            const response = await fetch('auth.php?action=login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ password })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.isAuthenticated = true;
                this.showDashboard();
                await this.loadDashboardData();
            } else {
                errorDiv.textContent = data.error || 'خطأ في تسجيل الدخول';
                errorDiv.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Login error:', error);
            errorDiv.textContent = 'خطأ في الاتصال بالخادم';
            errorDiv.classList.remove('hidden');
        }
    }
    
    async handleLogout() {
        try {
            await fetch('auth.php?action=logout', { method: 'POST' });
            this.isAuthenticated = false;
            this.showLogin();
        } catch (error) {
            console.error('Logout error:', error);
        }
    }
    
    showLogin() {
        document.getElementById('loginModal').classList.add('active');
        document.getElementById('dashboard').classList.add('hidden');
    }
    
    showDashboard() {
        document.getElementById('loginModal').classList.remove('active');
        document.getElementById('dashboard').classList.remove('hidden');
    }
    
    showSection(section) {
        // Update sidebar
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');
        
        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.add('hidden');
        });
        document.getElementById(`${section}Section`).classList.remove('hidden');
        
        this.currentSection = section;
        
        // Load section-specific data
        switch (section) {
            case 'categories':
                this.loadCategories();
                break;
            case 'products':
                this.loadProducts();
                break;
            case 'files':
                this.loadRecentFiles();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }
    
    async loadDashboardData() {
        try {
            // Load categories
            const categoriesResponse = await fetch('api.php?endpoint=categories&action=list');
            const categoriesData = await categoriesResponse.json();
            this.categories = categoriesData.data || [];
            
            // Load products
            const productsResponse = await fetch('api.php?endpoint=products&action=list');
            const productsData = await productsResponse.json();
            this.products = productsData.data || [];
            
            // Update dashboard stats
            document.getElementById('totalCategories').textContent = this.categories.length;
            document.getElementById('totalProducts').textContent = this.products.length;
            document.getElementById('featuredProducts').textContent = this.products.filter(p => p.is_featured).length;
            
            // Populate category filter
            this.populateCategoryFilter();
            
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }
    
    populateCategoryFilter() {
        const filter = document.getElementById('categoryFilter');
        filter.innerHTML = '<option value="">جميع الفئات</option>';
        
        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name_ar;
            filter.appendChild(option);
        });
    }
    
    async loadCategories() {
        try {
            const response = await fetch('api.php?endpoint=categories&action=list');
            const data = await response.json();
            
            if (data.success) {
                this.categories = data.data;
                this.renderCategoriesTable();
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }
    
    renderCategoriesTable() {
        const tbody = document.getElementById('categoriesTable');
        tbody.innerHTML = '';
        
        this.categories.forEach(category => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${category.name_ar}</div>
                    <div class="text-sm text-gray-500">${category.name_en}</div>
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">${category.description_ar || '-'}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${category.sort_order}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${category.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${category.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="dashboard.editCategory(${category.id})" class="text-shamra-green hover:text-shamra-light-green ml-2">تعديل</button>
                    <button onclick="dashboard.deleteCategory(${category.id})" class="text-red-600 hover:text-red-900">حذف</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    async loadProducts() {
        try {
            const response = await fetch('api.php?endpoint=products&action=list');
            const data = await response.json();
            
            if (data.success) {
                this.products = data.data;
                this.renderProductsTable();
            }
        } catch (error) {
            console.error('Error loading products:', error);
        }
    }
    
    renderProductsTable() {
        const tbody = document.getElementById('productsTable');
        tbody.innerHTML = '';
        
        this.products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                        ${product.image ? `<img src="uploads/products/${product.image}" class="w-full h-full object-cover rounded-lg" alt="${product.name_ar}">` : '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/></svg>'}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${product.name_ar}</div>
                    <div class="text-sm text-gray-500">${product.name_en}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${product.category_name_ar || '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}">
                        ${product.is_featured ? 'مميز' : 'عادي'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${product.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="dashboard.editProduct(${product.id})" class="text-shamra-green hover:text-shamra-light-green ml-2">تعديل</button>
                    <button onclick="dashboard.deleteProduct(${product.id})" class="text-red-600 hover:text-red-900">حذف</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    filterProducts(categoryId) {
        const filteredProducts = categoryId ? this.products.filter(p => p.category_id == categoryId) : this.products;
        // Re-render table with filtered products
        const tbody = document.getElementById('productsTable');
        tbody.innerHTML = '';
        
        filteredProducts.forEach(product => {
            // Same rendering logic as renderProductsTable
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                        ${product.image ? `<img src="uploads/products/${product.image}" class="w-full h-full object-cover rounded-lg" alt="${product.name_ar}">` : '<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/></svg>'}
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">${product.name_ar}</div>
                    <div class="text-sm text-gray-500">${product.name_en}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${product.category_name_ar || '-'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}">
                        ${product.is_featured ? 'مميز' : 'عادي'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${product.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${product.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="dashboard.editProduct(${product.id})" class="text-shamra-green hover:text-shamra-light-green ml-2">تعديل</button>
                    <button onclick="dashboard.deleteProduct(${product.id})" class="text-red-600 hover:text-red-900">حذف</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }
    
    // File upload handlers
    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('border-shamra-green');
    }
    
    handleFileDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('border-shamra-green');
        
        const files = Array.from(e.dataTransfer.files);
        this.uploadFiles(files);
    }
    
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.uploadFiles(files);
    }
    
    async uploadFiles(files) {
        const uploadType = document.getElementById('uploadType').value;
        const progressDiv = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('progressBar');
        const statusText = document.getElementById('uploadStatus');
        
        progressDiv.classList.remove('hidden');
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', uploadType);
            
            try {
                statusText.textContent = `جاري رفع ${file.name}...`;
                progressBar.style.width = `${((i + 1) / files.length) * 100}%`;
                
                const response = await fetch('api.php?endpoint=upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    console.log('File uploaded:', data.filename);
                } else {
                    console.error('Upload failed:', data.error);
                }
            } catch (error) {
                console.error('Upload error:', error);
            }
        }
        
        statusText.textContent = 'تم الرفع بنجاح!';
        setTimeout(() => {
            progressDiv.classList.add('hidden');
            progressBar.style.width = '0%';
        }, 2000);
        
        // Reload recent files
        this.loadRecentFiles();
    }
    
    loadRecentFiles() {
        // This would load recent files from the server
        // For now, we'll show a placeholder
        const container = document.getElementById('recentFiles');
        container.innerHTML = '<p class="text-gray-500 text-sm">لا توجد ملفات حديثة</p>';
    }
    
    async loadSettings() {
        try {
            const response = await fetch('api.php?endpoint=settings');
            const data = await response.json();
            
            if (data.success) {
                const settings = data.data;
                document.getElementById('companyNameAr').value = settings.company_name_ar || 'شركة شمرا الزراعية';
                document.getElementById('companyNameEn').value = settings.company_name_en || 'Shamra Agricultural Company';
                document.getElementById('taglineAr').value = settings.tagline_ar || 'الشريك الموثوق في الزراعة';
                document.getElementById('taglineEn').value = settings.tagline_en || 'Your Trusted Partner in Agriculture';
            }
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }
    
    async saveSettings(e) {
        e.preventDefault();
        
        const settings = {
            company_name_ar: document.getElementById('companyNameAr').value,
            company_name_en: document.getElementById('companyNameEn').value,
            tagline_ar: document.getElementById('taglineAr').value,
            tagline_en: document.getElementById('taglineEn').value
        };
        
        try {
            const response = await fetch('api.php?endpoint=settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(settings)
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('تم حفظ الإعدادات بنجاح');
            } else {
                alert('خطأ في حفظ الإعدادات');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            alert('خطأ في الاتصال بالخادم');
        }
    }
    
    // Modal functions (to be implemented)
    showCategoryModal(id = null) {
        // Implementation for category modal
        console.log('Show category modal', id);
    }
    
    showProductModal(id = null) {
        // Implementation for product modal
        console.log('Show product modal', id);
    }
    
    editCategory(id) {
        this.showCategoryModal(id);
    }
    
    editProduct(id) {
        this.showProductModal(id);
    }
    
    async deleteCategory(id) {
        if (confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
            try {
                const response = await fetch(`api.php?endpoint=categories&id=${id}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    await this.loadCategories();
                    await this.loadDashboardData();
                } else {
                    alert('خطأ في حذف الفئة');
                }
            } catch (error) {
                console.error('Error deleting category:', error);
                alert('خطأ في الاتصال بالخادم');
            }
        }
    }
    
    async deleteProduct(id) {
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
            try {
                const response = await fetch(`api.php?endpoint=products&id=${id}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    await this.loadProducts();
                    await this.loadDashboardData();
                } else {
                    alert('خطأ في حذف المنتج');
                }
            } catch (error) {
                console.error('Error deleting product:', error);
                alert('خطأ في الاتصال بالخادم');
            }
        }
    }
    
    changeLanguage(lang) {
        // Implementation for language switching
        console.log('Change language to:', lang);
    }
}

// Initialize dashboard
const dashboard = new AdminDashboard();

// Global function for section switching
function showSection(section) {
    dashboard.showSection(section);
}
